import 'dart:async';
import 'package:flutter/material.dart';
import 'package:befine/models/device_model.dart';
import 'package:befine/models/spray_model.dart';
import 'package:befine/services/ble_manager.dart';
import 'package:befine/services/device_service.dart';
import 'package:befine/theme/app_theme.dart';
import 'package:befine/routes/app_routes.dart';
import 'package:provider/provider.dart';

/// Widget for displaying device information without connection controls
class DeviceInfoWidget extends StatefulWidget {
  const DeviceInfoWidget({super.key});

  @override
  State<DeviceInfoWidget> createState() => _DeviceInfoWidgetState();
}

class _DeviceInfoWidgetState extends State<DeviceInfoWidget> {
  int? _batteryLevel;
  Timer? _batteryCheckTimer;
  SprayModel? _currentSpray;
  final int _leftDoses = 85; // Demo value for left doses

  @override
  void initState() {
    super.initState();
    _checkBatteryLevel();
    _batteryCheckTimer = Timer.periodic(
      const Duration(minutes: 5),
      (_) => _checkBatteryLevel(),
    );
    // Initialize with demo spray
    _currentSpray = SprayModel.demoSprays.first;
  }

  @override
  void dispose() {
    _batteryCheckTimer?.cancel();
    super.dispose();
  }

  Future<void> _checkBatteryLevel() async {
    final bleManager = Provider.of<BleManager>(context, listen: false);
    if (bleManager.connectionState == BleConnectionState.connected) {
      final batteryLevel = await bleManager.getDeviceBatteryLevel();
      if (mounted) {
        setState(() {
          _batteryLevel = batteryLevel;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<BleManager, DeviceService>(
      builder: (context, bleManager, deviceService, child) {
        final deviceInfo = _getDeviceInfo(deviceService, bleManager);

        return Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: AppTheme.surfaceColor,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with device icon and status
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor.withValues(alpha: 0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.devices_other,
                      color: AppTheme.primaryColor,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          deviceInfo['name'],
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: AppTheme.textPrimaryColor,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Container(
                              width: 8,
                              height: 8,
                              decoration: BoxDecoration(
                                color:
                                    deviceInfo['isConnected']
                                        ? Colors.green
                                        : Colors.red,
                                shape: BoxShape.circle,
                              ),
                            ),
                            const SizedBox(width: 8),
                            Text(
                              deviceInfo['isConnected']
                                  ? 'Connected'
                                  : 'Disconnected',
                              style: TextStyle(
                                color:
                                    deviceInfo['isConnected']
                                        ? Colors.green
                                        : Colors.red,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  // Gear icon for manage devices
                  GestureDetector(
                    onTap: () => _navigateToManageDevices(context),
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: AppTheme.primaryColor.withValues(alpha: 0.1),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.settings,
                        color: AppTheme.primaryColor,
                        size: 20,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),

              // Device details and spray info
              if (deviceInfo['isConnected']) ...[
                _buildConnectedDeviceContent(deviceInfo),
              ] else ...[
                _buildDisconnectedContent(),
              ],
            ],
          ),
        );
      },
    );
  }

  /// Get device information from services
  Map<String, dynamic> _getDeviceInfo(
    DeviceService deviceService,
    BleManager bleManager,
  ) {
    // Check if there's a connected device via BLE
    if (bleManager.connectionState == BleConnectionState.connected &&
        bleManager.connectedDevice != null) {
      final device = bleManager.connectedDevice!;
      final deviceId = device.remoteId.toString();

      // Get saved device info
      final savedDevice = deviceService.getDeviceById(deviceId);

      return {
        'name':
            savedDevice?.customName ??
            (device.platformName.isNotEmpty
                ? device.platformName
                : 'SmartInhealer'),
        'isConnected': true,
        'batteryLevel': _batteryLevel ?? savedDevice?.batteryLevel ?? 95,
      };
    }

    // Check if there are any saved devices
    final devices = deviceService.devices;
    if (devices.isNotEmpty) {
      final device =
          deviceService.lastConnectedDeviceId != null
              ? deviceService.getDeviceById(
                deviceService.lastConnectedDeviceId!,
              )
              : devices.first;

      if (device != null) {
        return {
          'name': device.customName ?? device.deviceType ?? 'SmartInhealer',
          'isConnected': device.isConnected ?? false,
          'batteryLevel': device.batteryLevel ?? 0,
        };
      }
    }

    // Default device info
    return {'name': 'No Device', 'isConnected': false, 'batteryLevel': 0};
  }

  /// Build connected device content
  Widget _buildConnectedDeviceContent(Map<String, dynamic> deviceInfo) {
    return Column(
      children: [
        // Battery Level and Device Image Row
        Row(
          children: [
            // Battery Level and Spray Info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        _getBatteryIcon(deviceInfo['batteryLevel']),
                        color: _getBatteryColor(deviceInfo['batteryLevel']),
                        size: 24,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Battery: ${deviceInfo['batteryLevel']}%',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: AppTheme.textPrimaryColor,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Spray Information
                  if (_currentSpray != null) ...[
                    const Text(
                      'Current Spray',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: AppTheme.textSecondaryColor,
                      ),
                    ),
                    const SizedBox(height: 8),
                    _buildSprayDetailRow(
                      'Name',
                      _currentSpray!.name.displayName,
                    ),
                    _buildSprayDetailRow(
                      'Dose Type',
                      _currentSpray!.doseType.displayName,
                    ),
                    _buildSprayDetailRow('Left Doses', '$_leftDoses doses'),
                  ],
                ],
              ),
            ),
            const SizedBox(width: 16),

            // Device Image (tappable to navigate to device info)
            GestureDetector(
              onTap: () => _navigateToDeviceInfo(context),
              child: Container(
                width: 100,
                height: 140,
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor.withValues(alpha: 0.05),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: AppTheme.primaryColor.withValues(alpha: 0.2),
                    width: 1,
                  ),
                ),
                child: Center(
                  child: Image.asset(
                    'assets/images/healer.png',
                    height: 80,
                    fit: BoxFit.contain,
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Build disconnected content
  Widget _buildDisconnectedContent() {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.grey.shade50,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey.shade200),
          ),
          child: Column(
            children: [
              Icon(
                Icons.bluetooth_disabled,
                size: 48,
                color: Colors.grey.shade400,
              ),
              const SizedBox(height: 12),
              const Text(
                'No Device Connected',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
              const SizedBox(height: 8),
              const Text(
                'Connect a device to start testing',
                style: TextStyle(
                  fontSize: 14,
                  color: AppTheme.textSecondaryColor,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: () {
              Navigator.of(context).pushNamed('/manage-devices');
            },
            icon: const Icon(Icons.bluetooth_searching, size: 20),
            label: const Text('Connect Device'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: 2,
            ),
          ),
        ),
      ],
    );
  }

  /// Build spray detail row
  Widget _buildSprayDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(
              color: AppTheme.textSecondaryColor,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              color: AppTheme.textPrimaryColor,
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  /// Get battery icon based on level
  IconData _getBatteryIcon(int batteryLevel) {
    if (batteryLevel >= 90) return Icons.battery_full;
    if (batteryLevel >= 60) return Icons.battery_5_bar;
    if (batteryLevel >= 40) return Icons.battery_3_bar;
    if (batteryLevel >= 20) return Icons.battery_2_bar;
    if (batteryLevel > 0) return Icons.battery_1_bar;
    return Icons.battery_0_bar;
  }

  /// Get battery color based on level
  Color _getBatteryColor(int batteryLevel) {
    if (batteryLevel >= 50) return Colors.green;
    if (batteryLevel >= 20) return Colors.orange;
    return Colors.red;
  }

  /// Navigate to device info page
  void _navigateToDeviceInfo(BuildContext context) {
    Navigator.pushNamed(context, AppRoutes.deviceInfo);
  }

  /// Navigate to manage devices page
  void _navigateToManageDevices(BuildContext context) {
    Navigator.pushNamed(context, AppRoutes.manageDevices);
  }
}
