# ESP32C6 4MB Flash Minimal Partition Table for BeFine SmartInhealer
# Maximum app space, minimal OTA support
# Total Flash: 4MB (4,194,304 bytes = 0x400000)
#
# Memory Layout:
# 0x0000   - 0x8000   : Bootloader (32KB)
# 0x8000   - 0x9000   : Partition Table (4KB) 
# 0x9000   - 0xE000   : NVS (20KB)
# 0xE000   - 0xF000   : PHY Init (4KB)
# 0xF000   - 0x10000  : Reserved (4KB)
# 0x10000  - 0x310000 : Factory App (3MB)
# 0x310000 - 0x312000 : OTA Data (8KB)
# 0x312000 - 0x400000 : SPIFFS (952KB)
#
# Name,     Type, SubType, Offset,   Size,     Flags
nvs,        data, nvs,     0x9000,   0x5000,   
phy_init,   data, phy,     0xe000,   0x1000,   
factory,    app,  factory, 0x10000,  0x300000, 
otadata,    data, ota,     0x310000, 0x2000,   
spiffs,     data, spiffs,  0x312000, 0xEE000,
