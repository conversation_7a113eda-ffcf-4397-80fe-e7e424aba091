# ESP32C6 4MB Flash Custom Partition Table for BeFine SmartInhealer
# Optimized for memory-constrained environment with OTA support
# Total Flash: 4MB (4,194,304 bytes = 0x400000)
#
# Memory Layout:
# 0x0000   - 0x8000   : Bootloader (32KB)
# 0x8000   - 0x9000   : Partition Table (4KB)
# 0x9000   - 0xF000   : NVS (24KB)
# 0xF000   - 0x10000  : PHY Init (4KB)
# 0x10000  - 0x200000 : Factory App (2MB)
# 0x200000 - 0x280000 : OTA_0 (512KB)
# 0x280000 - 0x300000 : OTA_1 (512KB)
# 0x300000 - 0x302000 : OTA Data (8KB)
# 0x302000 - 0x3F0000 : SPIFFS (952KB)
# 0x3F0000 - 0x400000 : Reserved (64KB)
#
# Name,     Type, SubType, Offset,   Size,     Flags
nvs,        data, nvs,     0x9000,   0x6000,
phy_init,   data, phy,     0xf000,   0x1000,
factory,    app,  factory, 0x10000,  0x1F0000,
ota_0,      app,  ota_0,   0x200000, 0x80000,
ota_1,      app,  ota_1,   0x280000, 0x80000,
otadata,    data, ota,     0x300000, 0x2000,
spiffs,     data, spiffs,  0x302000, 0xEE000,
