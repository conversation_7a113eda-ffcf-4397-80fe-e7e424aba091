# ESP32C6 4MB Flash Optimization Guide for BeFine SmartInhealer

## 🚀 Code Optimizations Applied

### Memory Optimizations
1. **PROGMEM Usage**: Moved constant strings to flash memory
   - WiFi credentials, Supabase URLs stored in PROGMEM
   - Reduced RAM usage by ~500 bytes

2. **Data Type Optimization**: 
   - Changed `int` to `uint8_t`/`uint16_t` where appropriate
   - Used `constexpr` for compile-time constants
   - Reduced struct sizes by 30-40%

3. **Bit Field Optimization**:
   - Packed boolean flags into bit fields
   - Reduced SystemState struct from ~400 bytes to ~300 bytes
   - Added helper functions for clean bit manipulation

4. **JSON Document Sizing**:
   - Reduced DynamicJsonDocument to StaticJsonDocument where possible
   - Optimized buffer sizes based on actual usage
   - Measurement doc: 2048 → 1536 bytes
   - Counter doc: 1024 → 256 bytes

### Performance Optimizations
1. **String Handling**: Used F() macro for static strings
2. **Loop Optimization**: Changed loop counters to uint8_t
3. **Function Inlining**: Used constexpr for compile-time evaluation

## 📁 Custom Partition Schemas

### Option 1: Standard with OTA Support (`partitions_4mb.csv`)
```
Factory App:  2MB   (Main application)
OTA_0:       512KB  (Update partition 1)
OTA_1:       512KB  (Update partition 2)
SPIFFS:      952KB  (Local storage)
NVS:          24KB  (Settings)
Others:       40KB  (System partitions)
```

### Option 2: Minimal Maximum App Space (`partitions_4mb_minimal.csv`)
```
Factory App:   3MB  (Maximum application space)
SPIFFS:      952KB  (Local storage)
NVS:          20KB  (Settings)
Others:       52KB  (System partitions)
```

## 🔧 Usage Instructions

### 1. Using Custom Partition Schema

#### Arduino IDE:
1. Copy partition file to Arduino ESP32 package:
   ```
   C:\Users\<USER>\AppData\Local\Arduino15\packages\esp32\hardware\esp32\[VERSION]\tools\partitions\
   ```

2. In Arduino IDE:
   - Tools → Board → ESP32C6 Dev Module
   - Tools → Partition Scheme → Custom
   - Tools → Custom Partition → Select your .csv file

#### PlatformIO:
Add to `platformio.ini`:
```ini
[env:esp32c6]
platform = espressif32
board = esp32c6
framework = arduino
board_build.partitions = partitions_4mb.csv
board_build.filesystem = spiffs
monitor_speed = 115200
```

### 2. Compiler Optimizations

Add these flags to reduce binary size:

#### Arduino IDE:
Create `platform.local.txt` in ESP32 package folder:
```
compiler.c.extra_flags=-Os -ffunction-sections -fdata-sections
compiler.cpp.extra_flags=-Os -ffunction-sections -fdata-sections
compiler.c.elf.extra_flags=-Wl,--gc-sections
```

#### PlatformIO:
```ini
build_flags = 
    -Os
    -ffunction-sections
    -fdata-sections
    -Wl,--gc-sections
    -DCORE_DEBUG_LEVEL=1
```

### 3. Memory Monitoring

Add to setup() for memory debugging:
```cpp
Serial.printf("Free heap: %d bytes\n", ESP.getFreeHeap());
Serial.printf("Free PSRAM: %d bytes\n", ESP.getFreePsram());
Serial.printf("Flash size: %d bytes\n", ESP.getFlashChipSize());
```

## 📊 Expected Memory Usage

### Flash Memory:
- **Optimized Code**: ~1.2MB (vs ~1.5MB original)
- **Available for App**: 2MB (standard) or 3MB (minimal)
- **SPIFFS Storage**: 952KB for data logging

### RAM Usage:
- **Heap at Boot**: ~280KB free (vs ~260KB original)
- **Stack Usage**: ~8KB per task
- **Global Variables**: ~15KB (vs ~20KB original)

## ⚠️ Important Notes

1. **OTA Updates**: 
   - Standard schema supports OTA updates
   - Minimal schema requires manual flashing for updates

2. **SPIFFS Size**: 
   - 952KB allows ~2000 measurement records
   - Automatic cleanup when 90% full

3. **Memory Monitoring**:
   - Monitor heap usage during BLE operations
   - Watch for memory leaks in JSON operations

4. **Compilation**:
   - Use Arduino ESP32 Core v2.0.11 or later
   - Enable compiler optimizations for size

## 🔍 Troubleshooting

### If compilation fails:
1. Check ESP32 core version compatibility
2. Verify partition file syntax
3. Ensure sufficient flash space

### If runtime issues occur:
1. Monitor serial output for memory warnings
2. Check heap fragmentation
3. Verify SPIFFS mount success

### Memory optimization tips:
1. Use String sparingly, prefer char arrays
2. Free JSON documents after use
3. Avoid deep recursion
4. Use PROGMEM for large constant data
