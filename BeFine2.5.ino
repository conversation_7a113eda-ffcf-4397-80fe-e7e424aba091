#include <Arduino.h>
#include <SensirionI2CSdp.h>
#include <Wire.h>
#include <Adafruit_SSD1306.h>
#include <driver/rtc_io.h>
#include <esp_sleep.h>
#include <SD.h>
#include <SPI.h>
#include <WiFi.h>
#include <NTPClient.h>
#include <WiFiUdp.h>
#include <SPIFFS.h>
#include <ArduinoJson.h>
#include <HTTPClient.h>
#include <WiFiClientSecure.h>
// BLE Libraries
#include <BLEDevice.h>
#include <BLEServer.h>
#include <BLEUtils.h>
#include <BLE2902.h>

// Constants - Store in PROGMEM to save RAM
const char supabaseUrl[] PROGMEM = "https://aqvdhhutjhattwgfawzk.supabase.co";
const char supabaseKey[] PROGMEM = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFxdmRoaHV0amhhdHR3Z2Zhd3prIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk3NTk5MjIsImV4cCI6MjA2NTMzNTkyMn0.xGZTdq0lp5LKm7TfvfWQNEVNWgn672GL_3R0xY7MBEg";
const char ssid[] PROGMEM = "SamiBenNjima";
const char password[] PROGMEM = "Samsam03";

// BLE Configuration - MUST match Flutter app exactly
#define SERVICE_UUID        "4fafc201-1fb5-459e-8fcc-c5c9c331914b"
#define DATA_CHAR_UUID      "beb5483e-36e1-4688-b7f5-ea07361b26a8"
#define STATUS_CHAR_UUID    "beb5483e-36e1-4688-b7f5-ea07361b26a9"
#define DEVICE_NAME         "SmartInhealer"

// Hardware Configuration - Use constexpr for compile-time constants
constexpr uint8_t DISPLAY_WIDTH = 128;
constexpr uint8_t DISPLAY_HEIGHT = 64;
constexpr uint8_t DISPLAY_ADDR = 0x3C;
constexpr uint8_t I2C_SDA = 11;
constexpr uint8_t I2C_SCL = 10;
constexpr uint8_t WAKEUP_BUTTON_PIN = 7;
constexpr uint8_t LED_PIN = 8;
constexpr uint8_t SD_MISO = 20;
constexpr uint8_t SD_MOSI = 18;
constexpr uint8_t SD_SCK = 19;
constexpr uint8_t SD_CS = 21;
constexpr uint8_t BATTERY_PIN = 5;  // GPIO5 for voltage divider circuit

// Battery Configuration - Optimize for memory
constexpr float MAX_VOLTAGE = 2.1f;  // 4.2V / 2 (voltage divider)
constexpr float MIN_VOLTAGE = 1.3f;  // 2.6V / 2 (voltage divider)
constexpr float ADC_REFERENCE = 3.3f;
constexpr uint16_t ADC_RESOLUTION = 4095;

// Timing Constants - Use smaller data types where possible
constexpr uint16_t INACTIVITY_TIMEOUT_MS = 30000;
constexpr uint16_t BUTTON_DEBOUNCE_MS = 300;
constexpr uint16_t DIP_DISPLAY_DURATION_MS = 7000;
constexpr uint16_t READY_DISPLAY_DURATION_MS = 15000;
constexpr uint16_t BELOW_THRESHOLD_DURATION_MS = 2000;
constexpr uint16_t REFRESH_INTERVAL_MS = 5000;
constexpr uint16_t WELCOME_SCREEN_DURATION_MS = 2000;
constexpr uint16_t STATUS_CHECK_INTERVAL_MS = 1000;

// Flow Constants - Optimize for memory
constexpr float FLOW_RATE_THRESHOLD = 0.2f;
constexpr float MIN_FLOW_RATE_TO_DRAW = 0.2f;
constexpr float LOW_FLOW_THRESHOLD = 2.5f;
constexpr float CALIBRATION_FACTOR = 0.1f;
constexpr float AIR_DENSITY = 1.204f;
constexpr uint8_t FLOW_BUFFER_SIZE = 64;

// Global Objects
Adafruit_SSD1306 display(DISPLAY_WIDTH, DISPLAY_HEIGHT, &Wire);
SensirionI2CSdp flowSensor;
WiFiUDP ntpUDP;
NTPClient timeClient(ntpUDP, "pool.ntp.org", 3600, 60000);

// BLE Objects
BLEServer* pServer = NULL;
BLECharacteristic* pDataCharacteristic = NULL;
BLECharacteristic* pStatusCharacteristic = NULL;
bool deviceConnected = false;
bool oldDeviceConnected = false;

// System Status Structure
struct SystemStatus {
  bool sdReady = false;
  bool spiffsReady = false;
  bool wifiConnected = false;
  bool bleConnected = false;
  float batteryPercentage = 0.0f;
  String storageType = "NO";
  bool statusChanged = true; // Flag to track if status needs update
};

// State Management - Optimized structure with bit fields and smaller types
struct SystemState {
  // Boolean flags packed into bit fields to save memory
  uint8_t flags1;
  static constexpr uint8_t DISPLAY_ACTIVE_BIT = 0;
  static constexpr uint8_t TEST_RUNNING_BIT = 1;
  static constexpr uint8_t DIP_DISPLAYED_BIT = 2;
  static constexpr uint8_t READY_DISPLAYED_BIT = 3;
  static constexpr uint8_t WELCOME_DISPLAYED_BIT = 4;
  static constexpr uint8_t SD_READY_BIT = 5;
  static constexpr uint8_t SPIFFS_READY_BIT = 6;
  static constexpr uint8_t WIFI_CONNECTED_BIT = 7;

  uint8_t flags2;
  static constexpr uint8_t BLE_TEST_RUNNING_BIT = 0;

  // Helper functions for bit manipulation
  bool isDisplayActive() const { return flags1 & (1 << DISPLAY_ACTIVE_BIT); }
  void setDisplayActive(bool val) { val ? (flags1 |= (1 << DISPLAY_ACTIVE_BIT)) : (flags1 &= ~(1 << DISPLAY_ACTIVE_BIT)); }

  bool isTestRunning() const { return flags1 & (1 << TEST_RUNNING_BIT); }
  void setTestRunning(bool val) { val ? (flags1 |= (1 << TEST_RUNNING_BIT)) : (flags1 &= ~(1 << TEST_RUNNING_BIT)); }

  bool isDipDisplayed() const { return flags1 & (1 << DIP_DISPLAYED_BIT); }
  void setDipDisplayed(bool val) { val ? (flags1 |= (1 << DIP_DISPLAYED_BIT)) : (flags1 &= ~(1 << DIP_DISPLAYED_BIT)); }

  bool isReadyDisplayed() const { return flags1 & (1 << READY_DISPLAYED_BIT); }
  void setReadyDisplayed(bool val) { val ? (flags1 |= (1 << READY_DISPLAYED_BIT)) : (flags1 &= ~(1 << READY_DISPLAYED_BIT)); }

  bool isWelcomeDisplayed() const { return flags1 & (1 << WELCOME_DISPLAYED_BIT); }
  void setWelcomeDisplayed(bool val) { val ? (flags1 |= (1 << WELCOME_DISPLAYED_BIT)) : (flags1 &= ~(1 << WELCOME_DISPLAYED_BIT)); }

  bool sdReady() const { return flags1 & (1 << SD_READY_BIT); }
  void setSdReady(bool val) { val ? (flags1 |= (1 << SD_READY_BIT)) : (flags1 &= ~(1 << SD_READY_BIT)); }

  bool spiffsReady() const { return flags1 & (1 << SPIFFS_READY_BIT); }
  void setSpiffsReady(bool val) { val ? (flags1 |= (1 << SPIFFS_READY_BIT)) : (flags1 &= ~(1 << SPIFFS_READY_BIT)); }

  bool wifiConnected() const { return flags1 & (1 << WIFI_CONNECTED_BIT); }
  void setWifiConnected(bool val) { val ? (flags1 |= (1 << WIFI_CONNECTED_BIT)) : (flags1 &= ~(1 << WIFI_CONNECTED_BIT)); }

  bool bleTestRunning() const { return flags2 & (1 << BLE_TEST_RUNNING_BIT); }
  void setBleTestRunning(bool val) { val ? (flags2 |= (1 << BLE_TEST_RUNNING_BIT)) : (flags2 &= ~(1 << BLE_TEST_RUNNING_BIT)); }

  float flowBuffer[FLOW_BUFFER_SIZE];
  uint8_t bufferIndex = 0;
  float maxInspiratoryFlow = 0.0f;
  uint16_t sessionCounter = 0;
  uint32_t uniqueIdCounter = 0;

  // BLE Test Control Variables
  uint32_t bleTestStartTime = 0;
  uint8_t bleSampleCount = 0;
  float bleTotalVolume = 0.0f;
  uint32_t lastBleMeasurementTime = 0;

  uint32_t lastActivity = 0;
  uint32_t lastButtonPress = 0;
  uint32_t testStartTime = 0;
  uint32_t belowThresholdStart = 0;
  uint32_t dipStartTime = 0;
  uint32_t readyStartTime = 0;
  uint32_t welcomeStartTime = 0;
  uint32_t lastStatusCheck = 0;

  String currentLogFile = "";

  // Constructor to initialize flags
  SystemState() : flags1(1 << DISPLAY_ACTIVE_BIT | 1 << WELCOME_DISPLAYED_BIT), flags2(0) {
    memset(flowBuffer, 0, sizeof(flowBuffer));
  }
} state;

SystemStatus systemStatus;
uint32_t lastRefreshTime = 0;

// Function declarations
void updateSystemStatus();
bool checkStorageStatus();
void displayStatusIcons();
void optimizedDisplayReady();
// BLE Function declarations
void initBLE();
void sendBLEMeasurementData();
void sendBLEStatusUpdate(String status, int samples, int maxSamples);
void sendBLEBatteryUpdate();
void startBLEMeasurement();
void stopBLEMeasurement();
void resetBLEMeasurement();
void handleBLETestExecution();
void takeBLEMeasurement();

// BLE Server Callbacks
class MyServerCallbacks: public BLEServerCallbacks {
    void onConnect(BLEServer* pServer) {
      deviceConnected = true;
      digitalWrite(LED_PIN, HIGH);
      Serial.println("=== BLE CLIENT CONNECTED ===");
      Serial.println("Device connected successfully!");

      // Send initial status when connected
      delay(1000);
      sendBLEStatusUpdate("ready", 0, 0);
      Serial.println("Initial BLE status sent to client");
    };

    void onDisconnect(BLEServer* pServer) {
      deviceConnected = false;
      Serial.println("=== BLE CLIENT DISCONNECTED ===");

      // Stop any running BLE test
      if (state.bleTestRunning) {
        stopBLEMeasurement();
        Serial.println("Stopped running BLE measurement");
      }

      // Restart advertising
      delay(500);
      pServer->startAdvertising();
      Serial.println("BLE Advertising restarted");
    }
};

// BLE Characteristic Callbacks
class MyDataCallbacks: public BLECharacteristicCallbacks {
    void onWrite(BLECharacteristic *pCharacteristic) {
      String rxValue = pCharacteristic->getValue().c_str();

      if (rxValue.length() > 0) {
        Serial.print("Received BLE command: ");
        Serial.println(rxValue);

        // Handle BLE commands
        if (rxValue == "START_MEASUREMENT") {
          startBLEMeasurement();
        } else if (rxValue == "STOP_MEASUREMENT") {
          stopBLEMeasurement();
        } else if (rxValue == "RESET_MEASUREMENT") {
          resetBLEMeasurement();
        } else if (rxValue == "GET_STATUS") {
          sendBLEStatusUpdate(state.bleTestRunning ? "measuring" : "ready",
                             state.bleSampleCount, 128);
        } else if (rxValue == "GET_BATTERY") {
          sendBLEBatteryUpdate();
        }
      }
    }
};

void setup() {
  Serial.begin(115200);
  delay(1000);
  Serial.println("\n=== Befine SmartInhaler v2.5  ===");

  // Initialize hardware
  Wire.begin(I2C_SDA, I2C_SCL);
  pinMode(WAKEUP_BUTTON_PIN, INPUT_PULLDOWN);
  pinMode(LED_PIN, OUTPUT);
  digitalWrite(LED_PIN, HIGH);

  // Initialize display
  if (!display.begin(SSD1306_SWITCHCAPVCC, DISPLAY_ADDR)) {
    Serial.println("[ERROR] Display init failed");
    while (1);
  }
  display.setTextColor(SSD1306_WHITE);
  showWelcome();

  // Initialize systems
  initStorage();
  connectWiFi();
  initFlowSensor();
  initBLE();
  
  // Setup sleep
  esp_sleep_enable_ext1_wakeup(1ULL << WAKEUP_BUTTON_PIN, ESP_EXT1_WAKEUP_ANY_HIGH);

  // Load data and reset
  loadCounters();
  resetTest();
  
  // Initial status update
  updateSystemStatus();

  Serial.println("[READY] System initialized");
}

void loop() {
  unsigned long now = millis();

  // Handle welcome screen
  if (handleWelcomeScreen(now)) return;

  // Handle button press
  if (handleButtonPress(now)) return;

  // Handle timeouts
  if (handleTimeouts(now)) return;

  // Update system status periodically
  if (now - state.lastStatusCheck >= STATUS_CHECK_INTERVAL_MS) {
    updateSystemStatus();
    state.lastStatusCheck = now;
  }

  // Refresh ready display
  if (state.isReadyDisplayed && 
      (now - lastRefreshTime >= REFRESH_INTERVAL_MS || systemStatus.statusChanged)) {
    optimizedDisplayReady();
    lastRefreshTime = now;
    systemStatus.statusChanged = false;
  }

  // Handle flow measurement
  if (state.isReadyDisplayed() || state.isTestRunning()) {
    handleFlowMeasurement();
  }

  // Handle BLE connection state changes
  if (!deviceConnected && oldDeviceConnected) {
    delay(500);
    pServer->startAdvertising();
    Serial.println("Start BLE advertising");
    oldDeviceConnected = deviceConnected;
  }

  if (deviceConnected && !oldDeviceConnected) {
    oldDeviceConnected = deviceConnected;
    Serial.println("BLE Device connected - sending initial status");
    sendBLEStatusUpdate("ready", 0, 0);
  }

  // Handle BLE test execution
  if (state.bleTestRunning() && deviceConnected) {
    handleBLETestExecution();
  }

  // Send periodic BLE battery updates
  static unsigned long lastBLEBatteryUpdate = 0;
  if (deviceConnected && millis() - lastBLEBatteryUpdate > 30000) { // Every 30 seconds
    sendBLEBatteryUpdate();
    lastBLEBatteryUpdate = millis();
  }

  delay(50);
}

bool handleWelcomeScreen(unsigned long now) {
  if (state.isWelcomeDisplayed() && (now - state.welcomeStartTime >= WELCOME_SCREEN_DURATION_MS)) {
    state.setWelcomeDisplayed(false);
    optimizedDisplayReady();
    return true;
  }
  return state.isWelcomeDisplayed();
}

bool handleButtonPress(unsigned long now) {
  if (digitalRead(WAKEUP_BUTTON_PIN) == HIGH && (now - state.lastButtonPress) > BUTTON_DEBOUNCE_MS) {
    resetTest();
    optimizedDisplayReady();
    state.lastButtonPress = now;
    return true;
  }
  return false;
}

bool handleTimeouts(unsigned long now) {
  // Ready display timeout
  if (state.isReadyDisplayed() && (now - state.readyStartTime > READY_DISPLAY_DURATION_MS)) {
    enterSleep();
    return true;
  }

  // Dip display timeout
  if (state.isDipDisplayed() && (now - state.dipStartTime > DIP_DISPLAY_DURATION_MS)) {
    resetTest();
    optimizedDisplayReady();
    return true;
  }

  // Inactivity timeout
  if (now - state.lastActivity > INACTIVITY_TIMEOUT_MS &&
      (now - state.lastButtonPress > BUTTON_DEBOUNCE_MS)) {
    enterSleep();
    return true;
  }

  return false;
}

void handleFlowMeasurement() {
  float pressure, temperature;
  if (!flowSensor.readMeasurement(pressure, temperature)) {
    float flowRate = calculateFlow(pressure);
    if (fabs(flowRate) > FLOW_RATE_THRESHOLD) {
      handleFlow(flowRate);
    } else {
      handleNoFlow();
    }
  }
}

void updateSystemStatus() {
  bool oldWifiStatus = systemStatus.wifiConnected;
  bool oldSdStatus = systemStatus.sdReady;
  bool oldSpiffsStatus = systemStatus.spiffsReady;
  bool oldBleStatus = systemStatus.bleConnected;
  float oldBatteryPercentage = systemStatus.batteryPercentage;

  // Update WiFi status
  systemStatus.wifiConnected = (WiFi.status() == WL_CONNECTED);

  // Update BLE status
  systemStatus.bleConnected = deviceConnected;

  // Update storage status
  systemStatus.sdReady = checkStorageStatus();
  systemStatus.spiffsReady = state.spiffsReady();

  // Update storage type string
  if (systemStatus.sdReady) {
    systemStatus.storageType = "SD";
  } else if (systemStatus.spiffsReady) {
    systemStatus.storageType = "INT";
  } else {
    systemStatus.storageType = "NO";
  }

  // Update battery percentage
  float batteryVoltage = readBatteryVoltage();
  systemStatus.batteryPercentage = constrain(
    (batteryVoltage - MIN_VOLTAGE) / (MAX_VOLTAGE - MIN_VOLTAGE) * 100, 0, 100);

  // Check if anything changed
  systemStatus.statusChanged = (
    oldWifiStatus != systemStatus.wifiConnected ||
    oldBleStatus != systemStatus.bleConnected ||
    oldSdStatus != systemStatus.sdReady ||
    oldSpiffsStatus != systemStatus.spiffsReady ||
    abs(oldBatteryPercentage - systemStatus.batteryPercentage) > 1.0f
  );

  // Update global state variables for backward compatibility
  state.setSdReady(systemStatus.sdReady);
  state.setSpiffsReady(systemStatus.spiffsReady);
  state.setWifiConnected(systemStatus.wifiConnected);
}

bool checkStorageStatus() {
  // Try to access SD card
  File testFile = SD.open("/", FILE_READ);
  if (testFile) {
    testFile.close();
    return true;
  }
  return false;
}

void optimizedDisplayReady() {
  display.clearDisplay();
  
  // Main ready message
  display.setTextSize(2);
  display.setCursor(0, 10);
  display.println("Ready!");
  // Status line
  display.setTextSize(1);
  display.setCursor(0, 35);
  display.print(systemStatus.storageType);
  display.print(" | S#");
  display.println(state.sessionCounter);  
  // Instruction
  display.setCursor(0, 45);
  display.println("Breathe in to start");
  

  
  // Display status icons
  displayStatusIcons();
  
  display.display();

  state.setReadyDisplayed(true);
  state.setDipDisplayed(false);
  state.readyStartTime = millis();
}

void displayStatusIcons() {
  // Battery icon
  displayBatteryIcon();

  // WiFi icon
  displayWiFiIcon();

  // BLE icon
  displayBLEIcon();
}

void displayBatteryIcon() {
  const int batteryWidth = 12;
  const int batteryHeight = 6;
  const int batteryX = display.width() - batteryWidth - 2;
  const int batteryY = 2;

  // Battery outline
  display.drawRect(batteryX, batteryY, batteryWidth, batteryHeight, SSD1306_WHITE);
  display.fillRect(batteryX + batteryWidth + 1, batteryY + 2, 2, 2, SSD1306_WHITE);

  // Battery fill
  int fillWidth = (batteryWidth - 2) * systemStatus.batteryPercentage / 100;
  display.fillRect(batteryX + 1, batteryY + 1, fillWidth, batteryHeight - 2, SSD1306_WHITE);

  // Battery percentage text
  display.setTextSize(1);
  display.setCursor(batteryX - 25, batteryY);
  display.print((int)systemStatus.batteryPercentage);
  display.print("%");
}

void displayWiFiIcon() {
  if (!systemStatus.wifiConnected) return;

  const int wifiX = display.width() - 15;
  const int wifiY = 20;

  // WiFi signal bars
  display.drawRect(wifiX, wifiY, 2, 5, SSD1306_WHITE);
  display.drawRect(wifiX + 3, wifiY - 2, 2, 7, SSD1306_WHITE);
  display.drawRect(wifiX + 6, wifiY - 4, 2, 9, SSD1306_WHITE);
  display.drawRect(wifiX + 9, wifiY - 6, 2, 11, SSD1306_WHITE);
}

void displayBLEIcon() {
  if (!systemStatus.bleConnected) return;

  const int bleX = display.width() - 30;
  const int bleY = 20;

  // BLE symbol (simplified Bluetooth icon)
  display.drawLine(bleX, bleY, bleX + 6, bleY - 3, SSD1306_WHITE);
  display.drawLine(bleX, bleY, bleX + 6, bleY + 3, SSD1306_WHITE);
  display.drawLine(bleX + 6, bleY - 3, bleX + 3, bleY, SSD1306_WHITE);
  display.drawLine(bleX + 6, bleY + 3, bleX + 3, bleY, SSD1306_WHITE);
  display.drawLine(bleX + 3, bleY - 6, bleX + 3, bleY + 6, SSD1306_WHITE);
}

void initFlowSensor() {
  flowSensor.begin(Wire, SDP3X_I2C_ADDRESS_0);
  flowSensor.stopContinuousMeasurement();
  delay(50);
  flowSensor.startContinuousMeasurementWithDiffPressureTCompAndAveraging();
}

void showWelcome() {
  display.clearDisplay();
  display.setTextSize(2);
  display.setCursor(0, 0);
  display.println("Befine");
  display.setTextSize(1);
  display.setCursor(0, 30);
  display.println("SmartInhaler v2.5");
  display.setCursor(0, 45);
  display.print("PFE : 2024/25");
  display.display();

  state.isWelcomeDisplayed = true;
  state.welcomeStartTime = millis();
}

float readBatteryVoltage() {
  int rawValue = analogRead(BATTERY_PIN);
  return (rawValue / (float)ADC_RESOLUTION) * ADC_REFERENCE;
}

void initStorage() {
  Serial.println("[INIT] Initializing storage...");
  SPI.begin(SD_SCK, SD_MISO, SD_MOSI, SD_CS);

  if (SD.begin(SD_CS)) {
    state.sdReady = true;
    Serial.println("[OK] SD card ready");
  }

  if (SPIFFS.begin(true)) {
    state.spiffsReady = true;
    Serial.println("[OK] SPIFFS ready");
  }

  updateLogFile();
}

void loadCounters() {
  const char* counterFile = "/counters.json";
  File file = state.sdReady() ? SD.open(counterFile, FILE_READ) :
             (state.spiffsReady() ? SPIFFS.open(counterFile, FILE_READ) : File());

  if (file) {
    String content = file.readString();
    file.close();

    StaticJsonDocument<256> doc;  // Reduced size for counters only
    if (deserializeJson(doc, content) == DeserializationError::Ok) {
      state.sessionCounter = doc["sessionCounter"] | 0;
      state.uniqueIdCounter = doc["uniqueIdCounter"] | 0;
      Serial.println("[OK] Loaded counters - Session: " + String(state.sessionCounter) +
                    ", ID: " + String(state.uniqueIdCounter));
    }
  }
}

void saveCounters() {
  const char* counterFile = "/counters.json";
  StaticJsonDocument<256> doc;  // Reduced size
  doc["sessionCounter"] = state.sessionCounter;
  doc["uniqueIdCounter"] = state.uniqueIdCounter;
  doc["lastUpdate"] = getTimestamp();

  String jsonData;
  serializeJson(doc, jsonData);

  File file = state.sdReady() ? SD.open(counterFile, FILE_WRITE) :
             (state.spiffsReady() ? SPIFFS.open(counterFile, FILE_WRITE) : File());

  if (file) {
    file.print(jsonData);
    file.close();
    Serial.println("[OK] Counters saved");
  }
}

String generateUniqueSessionId() {
  state.uniqueIdCounter++;
  String timestamp = String(millis());
  String deviceId = WiFi.macAddress();
  deviceId.replace(":", "");
  return "BEF" + deviceId.substring(6) + "_" + String(state.uniqueIdCounter, HEX);
}

void updateLogFile() {
  String date = getDateString();
  state.currentLogFile = "/spirometry_" + date + ".json";
  ensureLogFile();
}

void ensureLogFile() {
  bool exists = state.sdReady ? SD.exists(state.currentLogFile) : 
               (state.spiffsReady ? SPIFFS.exists(state.currentLogFile) : false);
  if (exists) return;

  File file = state.sdReady ? SD.open(state.currentLogFile, FILE_WRITE) : 
             (state.spiffsReady ? SPIFFS.open(state.currentLogFile, FILE_WRITE) : File());

  if (file) {
    file.println("{");
    file.println("  \"device_info\": {");
    file.println("    \"name\": \"Befine SmartInhaler\",");
    file.println("    \"version\": \"2.5\",");
    file.println("    \"created\": \"" + getTimestamp() + "\"");
    file.println("  },");
    file.println("  \"measurements\": [");
    file.println("  ]");
    file.println("}");
    file.close();
    Serial.println("[OK] Created organized log file: " + state.currentLogFile);
  }
}

void connectWiFi() {
  Serial.println(F("[INIT] Connecting to WiFi..."));

  // Read PROGMEM strings to RAM temporarily
  char ssid_buf[32], pass_buf[32];
  strcpy_P(ssid_buf, ssid);
  strcpy_P(pass_buf, password);

  WiFi.begin(ssid_buf, pass_buf);

  uint8_t attempts = 0;
  while (WiFi.status() != WL_CONNECTED && attempts < 10) {
    delay(500);
    attempts++;
  }

  bool connected = (WiFi.status() == WL_CONNECTED);
  state.setWifiConnected(connected);
  if (connected) {
    Serial.println("[OK] WiFi connected: " + WiFi.localIP().toString());
    timeClient.begin();
    timeClient.update();
  } else {
    Serial.println(F("[WARNING] WiFi failed"));
  }
}

String getDateString() {
  if (state.wifiConnected && timeClient.isTimeSet()) {
    timeClient.update();
    time_t rawtime = timeClient.getEpochTime();
    struct tm* timeinfo = localtime(&rawtime);
    char buffer[11];
    strftime(buffer, sizeof(buffer), "%Y-%m-%d", timeinfo);
    return String(buffer);
  }
  return "offline";
}

String getTimestamp() {
  if (state.wifiConnected && timeClient.isTimeSet()) {
    timeClient.update();
    time_t rawtime = timeClient.getEpochTime();
    struct tm* timeinfo = localtime(&rawtime);
    char buffer[20];
    strftime(buffer, sizeof(buffer), "%Y-%m-%d %H:%M:%S", timeinfo);
    return String(buffer);
  }

  unsigned long ms = millis();
  unsigned long s = ms / 1000;
  return String(s / 3600) + ":" + String((s % 3600) / 60) + ":" + String(s % 60);
}

String getTimeOnly() {
  if (state.wifiConnected && timeClient.isTimeSet()) {
    timeClient.update();
    time_t rawtime = timeClient.getEpochTime();
    struct tm* timeinfo = localtime(&rawtime);
    char buffer[9];
    strftime(buffer, sizeof(buffer), "%H:%M:%S", timeinfo);
    return String(buffer);
  }

  unsigned long ms = millis();
  unsigned long s = ms / 1000;
  char buffer[9];
  sprintf(buffer, "%02lu:%02lu:%02lu", s / 3600, (s % 3600) / 60, s % 60);
  return String(buffer);
}

void resetTest() {
  memset(state.flowBuffer, 0, sizeof(state.flowBuffer));
  state.bufferIndex = 0;
  state.maxInspiratoryFlow = 0.0f;
  state.isTestRunning = false;
  state.isDipDisplayed = false;
  state.isReadyDisplayed = false;
  state.belowThresholdStart = 0;
  state.testStartTime = 0;
  digitalWrite(LED_PIN, HIGH);
  state.lastActivity = millis();
}

float calculateFlow(float pressure) {
  float flow = sqrt(fabs(pressure) / AIR_DENSITY) * CALIBRATION_FACTOR * 1.0f;
  return (pressure < 0) ? -flow : flow;
}

void handleFlow(float flowRate) {
  if (!state.isTestRunning && state.isReadyDisplayed) {
    state.isTestRunning = true;
    state.testStartTime = millis();
    Serial.println("[TEST] Started");
  }

  state.isReadyDisplayed = false;
  state.lastActivity = millis();

  if (flowRate < 0 && fabs(flowRate) > fabs(state.maxInspiratoryFlow)) {
    state.maxInspiratoryFlow = flowRate;
  }

  state.flowBuffer[state.bufferIndex] = flowRate;
  state.bufferIndex = (state.bufferIndex + 1) % FLOW_BUFFER_SIZE;

  displayFlow();
  state.belowThresholdStart = millis();
}

void handleNoFlow() {
  if (state.isTestRunning && !state.isDipDisplayed && !state.isReadyDisplayed) {
    if (state.belowThresholdStart == 0) {
      state.belowThresholdStart = millis();
    } else if (millis() - state.belowThresholdStart >= BELOW_THRESHOLD_DURATION_MS) {
      if (fabs(state.maxInspiratoryFlow) > 0) {
        float result = fabs(state.maxInspiratoryFlow);
        displayResult(result);
        logMeasurement(result);
        state.isDipDisplayed = true;
        state.dipStartTime = millis();
        state.isTestRunning = false;
        Serial.println("[TEST] Completed: " + String(result) + " L/s");
      } else {
        resetTest();
        optimizedDisplayReady();
      }
    }
  }
}

void displayFlow() {
  display.clearDisplay();

  display.setTextSize(1);
  display.setCursor(0, 0);
  display.print("Peak: ");
  display.print(fabs(state.maxInspiratoryFlow), 1);
  display.println(" L/s");

  const int graphY = 15;
  const int graphH = 35;
  const int graphW = DISPLAY_WIDTH;

  float maxFlow = 0;
  for (int i = 0; i < FLOW_BUFFER_SIZE; i++) {
    maxFlow = max(maxFlow, fabs(state.flowBuffer[i]));
  }

  if (maxFlow > 0) {
    for (int x = 0; x < graphW; x++) {
      int bufferPos = (state.bufferIndex - FLOW_BUFFER_SIZE + (x / 2) + FLOW_BUFFER_SIZE) % FLOW_BUFFER_SIZE;
      float val = fabs(state.flowBuffer[bufferPos]);

      if (val > MIN_FLOW_RATE_TO_DRAW) {
        int barHeight = constrain((val / maxFlow) * graphH, 0, graphH);
        display.drawLine(x, graphY, x, graphY + barHeight, SSD1306_WHITE);
      }
    }
  }

  display.drawLine(0, graphY, graphW - 1, graphY, SSD1306_WHITE);

  display.setCursor(0, 50);
  display.print("Buf: ");
  display.print(state.bufferIndex);
  display.print("/");
  display.print(FLOW_BUFFER_SIZE);

  display.display();
}

void displayResult(float result) {
  display.clearDisplay();
  display.setTextSize(1);
  display.setCursor(0, 0);
  display.println("Result:");
  display.setTextSize(2);
  display.setCursor(0, 20);
  display.print(result, 1);
  display.println(" L/s");
  display.setTextSize(1);
  display.setCursor(0, 45);
  display.print("Session #");
  display.println(state.sessionCounter);
  display.println("Press for new test");
  display.display();

  digitalWrite(LED_PIN, result < LOW_FLOW_THRESHOLD ? LOW : HIGH);
}

void logMeasurement(float peakFlow) {
  if (!state.sdReady() && !state.spiffsReady()) return;

  state.sessionCounter++;
  String sessionId = generateUniqueSessionId();
  updateLogFile();

  StaticJsonDocument<1536> measurementDoc;  // Reduced size

  measurementDoc["session_id"] = sessionId;
  measurementDoc["date"] = getDateString();
  measurementDoc["time"] = getTimeOnly();
  measurementDoc["PIF"] = round(peakFlow * 10.0) / 10.0;
  measurementDoc["battery_level"] = readBatteryVoltage();

  JsonArray flowArray = measurementDoc.createNestedArray("flow_buffer");
  for (uint8_t i = 0; i < FLOW_BUFFER_SIZE; i++) {
    if (fabs(state.flowBuffer[i]) > 0.001f) {
      flowArray.add(round(state.flowBuffer[i] * 100.0) / 100.0);
    }
  }

  String measurementJson;
  serializeJson(measurementDoc, measurementJson);

  if (appendToLog(measurementJson)) {
    saveCounters();
    Serial.println("[OK] Data logged successfully - ID: " + sessionId + " | PIF: " + String(peakFlow) + " L/s");
    uploadToSupabase(measurementJson);
  } else {
    Serial.println(F("[ERROR] Failed to log data"));
  }
}

bool appendToLog(const String& measurementJson) {
  File file = state.sdReady ? SD.open(state.currentLogFile, FILE_READ) : 
             (state.spiffsReady ? SPIFFS.open(state.currentLogFile, FILE_READ) : File());

  if (!file) return false;

  String content = file.readString();
  file.close();

  int measurementsEnd = content.lastIndexOf("  ]");
  if (measurementsEnd == -1) return false;

  int measurementsStart = content.indexOf("\"measurements\": [") + 17;
  String measurementsSection = content.substring(measurementsStart, measurementsEnd);
  measurementsSection.trim();
  bool needComma = measurementsSection.length() > 0 && measurementsSection != "";

  String newContent = content.substring(0, measurementsEnd);
  if (needComma) newContent += ",\n";
  newContent += "    " + measurementJson + "\n";
  newContent += content.substring(measurementsEnd);

  file = state.sdReady ? SD.open(state.currentLogFile, FILE_WRITE) : 
        (state.spiffsReady ? SPIFFS.open(state.currentLogFile, FILE_WRITE) : File());

  if (!file) return false;

  file.print(newContent);
  file.close();
  return true;
}

void uploadToSupabase(const String& measurementJson) {
  if (!state.wifiConnected()) {
    Serial.println(F("[ERROR] WiFi not connected"));
    return;
  }

  WiFiClientSecure client;
  client.setInsecure();

  HTTPClient https;

  // Read PROGMEM strings
  char url_buf[128], key_buf[256];
  strcpy_P(url_buf, supabaseUrl);
  strcpy_P(key_buf, supabaseKey);

  String url = String(url_buf) + "/rest/v1/measurements";
  https.begin(client, url);
  https.addHeader(F("Content-Type"), F("application/json"));
  https.addHeader(F("apikey"), key_buf);
  https.addHeader(F("Authorization"), "Bearer " + String(key_buf));
  https.addHeader(F("Prefer"), F("return=minimal"));

  int httpCode = https.POST(measurementJson);
  if (httpCode > 0) {
    if (httpCode == HTTP_CODE_CREATED) {
      Serial.println(F("[OK] Data uploaded to Supabase"));
    } else {
      Serial.printf("[ERROR] HTTP error: %d\n", httpCode);
      Serial.println(https.getString());
    }
  } else {
    Serial.printf("[ERROR] HTTP request failed: %s\n", https.errorToString(httpCode).c_str());
  }

  https.end();
}

void enterSleep() {
  Serial.println("[INFO] Entering sleep mode");
  Serial.print("[INFO] Sessions: ");
  Serial.println(state.sessionCounter);

  display.clearDisplay();
  display.setTextSize(1);
  display.setCursor(0, 20);
  display.println("Sleeping ...");
  display.display();
  delay(1000);

  display.clearDisplay();
  display.display();

  saveCounters();

  if (state.wifiConnected) WiFi.disconnect(true);
  if (deviceConnected) {
    pServer->getAdvertising()->stop();
    BLEDevice::deinit(false);
  }
  if (state.sdReady) SD.end();
  if (state.spiffsReady) SPIFFS.end();

  SPI.end();
  Wire.end();

  esp_deep_sleep_start();
}

// ===== BLE FUNCTIONS =====

void initBLE() {
  Serial.println("[INIT] Initializing BLE...");

  // Initialize BLE with device name
  BLEDevice::init(DEVICE_NAME);
  pServer = BLEDevice::createServer();
  pServer->setCallbacks(new MyServerCallbacks());

  // Create BLE Service
  BLEService *pService = pServer->createService(SERVICE_UUID);

  // Create Data Characteristic (Read/Write/Notify)
  pDataCharacteristic = pService->createCharacteristic(
                         DATA_CHAR_UUID,
                         BLECharacteristic::PROPERTY_READ |
                         BLECharacteristic::PROPERTY_WRITE |
                         BLECharacteristic::PROPERTY_NOTIFY
                       );
  pDataCharacteristic->setCallbacks(new MyDataCallbacks());
  pDataCharacteristic->addDescriptor(new BLE2902());

  // Create Status Characteristic (Read/Notify)
  pStatusCharacteristic = pService->createCharacteristic(
                           STATUS_CHAR_UUID,
                           BLECharacteristic::PROPERTY_READ |
                           BLECharacteristic::PROPERTY_NOTIFY
                         );
  pStatusCharacteristic->addDescriptor(new BLE2902());

  // Start the service
  pService->start();

  // Configure and start advertising
  BLEAdvertising *pAdvertising = BLEDevice::getAdvertising();
  pAdvertising->addServiceUUID(SERVICE_UUID);
  pAdvertising->setScanResponse(true);
  pAdvertising->setMinPreferred(0x06);  // 7.5ms
  pAdvertising->setMaxPreferred(0x12);  // 22.5ms

  // Start advertising
  BLEDevice::startAdvertising();

  Serial.println("[OK] BLE initialized and advertising started");
  Serial.println("Device name: " + String(DEVICE_NAME));
  Serial.println("Service UUID: " + String(SERVICE_UUID));
}

void startBLEMeasurement() {
  if (!deviceConnected) return;

  Serial.println("=== STARTING BLE MEASUREMENT ===");
  Serial.println("Initializing SDP33 flow measurement for BLE...");

  state.bleTestRunning = true;
  state.bleTestStartTime = millis();
  state.bleSampleCount = 0;
  state.bleTotalVolume = 0.0f;
  state.lastBleMeasurementTime = millis();

  sendBLEStatusUpdate("measuring", state.bleSampleCount, 128);
  Serial.println("BLE Measurement started successfully");
}

void stopBLEMeasurement() {
  if (!deviceConnected) return;

  Serial.println("Stopping BLE measurement...");
  state.bleTestRunning = false;
  sendBLEStatusUpdate("stopped", state.bleSampleCount, 128);
}

void resetBLEMeasurement() {
  if (!deviceConnected) return;

  Serial.println("=== RESETTING BLE MEASUREMENT ===");
  state.bleTestRunning = false;
  state.bleSampleCount = 0;
  state.bleTotalVolume = 0.0f;
  sendBLEStatusUpdate("ready", 0, 0);
  Serial.println("BLE Measurement reset complete");
}

void handleBLETestExecution() {
  const int BLE_SAMPLE_INTERVAL = 50; // 50ms = 20Hz sampling rate
  const int BLE_MAX_SAMPLES = 128;

  // Check if it's time for a new measurement
  if (millis() - state.lastBleMeasurementTime >= BLE_SAMPLE_INTERVAL) {
    if (state.bleSampleCount < BLE_MAX_SAMPLES) {
      // Take a measurement from SDP33
      takeBLEMeasurement();

      state.bleSampleCount++;
      state.lastBleMeasurementTime = millis();

      // Update progress every 10 samples to avoid flooding
      if (state.bleSampleCount % 10 == 0) {
        sendBLEStatusUpdate("measuring", state.bleSampleCount, BLE_MAX_SAMPLES);
      }
    } else {
      // Test complete
      state.bleTestRunning = false;
      sendBLEStatusUpdate("complete", state.bleSampleCount, BLE_MAX_SAMPLES);
      Serial.println("=== BLE TEST COMPLETED ===");
      Serial.println("Total samples: " + String(state.bleSampleCount));
      Serial.println("Total volume: " + String(state.bleTotalVolume) + " L");
    }
  }
}

void takeBLEMeasurement() {
  float pressure, temperature;

  // Read measurement from SDP33 sensor using existing flow sensor object
  if (!flowSensor.readMeasurement(pressure, temperature)) {
    // Use existing calculateFlow function for consistency
    float flowRate = calculateFlow(pressure);

    // Store in flow buffer for BLE transmission
    state.flowBuffer[state.bufferIndex] = flowRate;
    state.bufferIndex = (state.bufferIndex + 1) % FLOW_BUFFER_SIZE;

    // Calculate volume increment (flow * time interval)
    float volumeIncrement = fabs(flowRate) * (50 / 1000.0); // 50ms interval in seconds
    state.bleTotalVolume += volumeIncrement;

    // Track maximum inspiratory flow for BLE
    if (flowRate < 0 && fabs(flowRate) > fabs(state.maxInspiratoryFlow)) {
      state.maxInspiratoryFlow = flowRate;
    }

    // Send measurement data via BLE
    sendBLEMeasurementData();

    if (state.bleSampleCount % 20 == 0) {
      Serial.println("BLE SDP33 - Pressure: " + String(pressure) +
                    " Pa, Flow: " + String(flowRate * 60.0) +
                    " L/min, Volume: " + String(state.bleTotalVolume) + " L");
    }
  } else {
    Serial.println("Error reading SDP33 sensor for BLE");
  }
}

void sendBLEMeasurementData() {
  if (!deviceConnected || !pDataCharacteristic) return;

  float time = (millis() - state.bleTestStartTime) / 1000.0;

  // Get current flow rate from the latest measurement
  float currentFlow = 0.0f;
  if (state.bufferIndex > 0) {
    currentFlow = state.flowBuffer[state.bufferIndex - 1] * 60.0f; // Convert to L/min
  }

  // Create JSON data matching Flutter app expectations
  StaticJsonDocument<200> doc;
  doc["time"] = time;
  doc["flow"] = currentFlow;
  doc["volume"] = state.bleTotalVolume;
  doc["sample"] = state.bleSampleCount;
  doc["progress"] = (float)state.bleSampleCount / 128 * 100.0;

  String jsonString;
  serializeJson(doc, jsonString);

  pDataCharacteristic->setValue(jsonString.c_str());
  pDataCharacteristic->notify();

  if (state.bleSampleCount % 20 == 0) {
    Serial.println("BLE Sent: " + jsonString);
  }
}

void sendBLEStatusUpdate(String status, int samples, int maxSamples) {
  if (!deviceConnected || !pStatusCharacteristic) return;

  int batteryLevel = (int)systemStatus.batteryPercentage;
  float progress = maxSamples > 0 ? (float)samples / maxSamples * 100.0 : 0.0;

  // Create JSON status matching Flutter app expectations
  StaticJsonDocument<200> doc;
  doc["status"] = status;
  doc["samples"] = samples;
  doc["maxSamples"] = maxSamples;
  doc["progress"] = progress;
  doc["battery"] = batteryLevel;
  doc["timestamp"] = millis();

  String jsonString;
  serializeJson(doc, jsonString);

  pStatusCharacteristic->setValue(jsonString.c_str());
  pStatusCharacteristic->notify();

  Serial.println("BLE Status: " + jsonString);
}

void sendBLEBatteryUpdate() {
  if (!deviceConnected) return;

  int batteryLevel = (int)systemStatus.batteryPercentage;
  Serial.println("BLE Battery level: " + String(batteryLevel) + "%");

  // Send battery update via status characteristic
  sendBLEStatusUpdate(state.bleTestRunning ? "measuring" : "ready",
                     state.bleSampleCount, 128);
}